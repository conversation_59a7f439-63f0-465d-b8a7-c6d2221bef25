"""
Session Manager for browser automation
Handles browser session pooling, task tracking, and resource management
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aioredis
from aioredis import Redis

from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import SessionException
from app.models.browser_automation import BrowserSession, TaskStatus


class SessionManager(LoggerMixin):
    """Manages browser sessions and task tracking"""
    
    def __init__(self):
        self.redis: Optional[Redis] = None
        self.active_sessions: Dict[str, BrowserSession] = {}
        self.task_registry: Dict[str, Dict[str, Any]] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
    
    async def initialize(self):
        """Initialize the session manager"""
        try:
            # Connect to Redis
            self.redis = await aioredis.from_url(
                settings.REDIS_URL,
                db=settings.REDIS_SESSION_DB,
                encoding="utf-8",
                decode_responses=True
            )
            
            # Test Redis connection
            await self.redis.ping()
            self.log_info("Redis connection established")
            
            # Start cleanup task
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
            
            self.log_info("Session manager initialized successfully")
            
        except Exception as e:
            self.log_error(f"Failed to initialize session manager: {e}")
            raise SessionException(f"Session manager initialization failed: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            # Cancel cleanup task
            if self._cleanup_task:
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass
            
            # Close Redis connection
            if self.redis:
                await self.redis.close()
            
            # Clear active sessions
            self.active_sessions.clear()
            self.task_registry.clear()
            
            self.log_info("Session manager cleanup completed")
            
        except Exception as e:
            self.log_error(f"Error during session manager cleanup: {e}")
    
    async def create_session(self, user_id: str, task_id: str) -> BrowserSession:
        """Create a new browser session"""
        try:
            session_id = f"{user_id}_{task_id}_{uuid.uuid4().hex[:8]}"
            
            session = BrowserSession(
                session_id=session_id,
                created_at=datetime.now(),
                last_activity=datetime.now()
            )
            
            # Store in memory
            self.active_sessions[session_id] = session
            
            # Store in Redis with expiration
            await self._store_session_in_redis(session)
            
            self.log_info(f"Created browser session: {session_id}")
            return session
            
        except Exception as e:
            self.log_error(f"Failed to create session: {e}")
            raise SessionException(f"Session creation failed: {e}")
    
    async def get_session(self, session_id: str) -> Optional[BrowserSession]:
        """Get an existing browser session"""
        try:
            # Check memory first
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                session.last_activity = datetime.now()
                return session
            
            # Check Redis
            session_data = await self.redis.hgetall(f"session:{session_id}")
            if session_data:
                session = BrowserSession(**session_data)
                self.active_sessions[session_id] = session
                return session
            
            return None
            
        except Exception as e:
            self.log_error(f"Failed to get session {session_id}: {e}")
            return None
    
    async def update_session(self, session: BrowserSession):
        """Update an existing browser session"""
        try:
            session.last_activity = datetime.now()
            
            # Update memory
            self.active_sessions[session.session_id] = session
            
            # Update Redis
            await self._store_session_in_redis(session)
            
        except Exception as e:
            self.log_error(f"Failed to update session {session.session_id}: {e}")
            raise SessionException(f"Session update failed: {e}")
    
    async def close_session(self, session_id: str):
        """Close and cleanup a browser session"""
        try:
            # Remove from memory
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            # Remove from Redis
            await self.redis.delete(f"session:{session_id}")
            
            self.log_info(f"Closed browser session: {session_id}")
            
        except Exception as e:
            self.log_error(f"Failed to close session {session_id}: {e}")
    
    async def register_task(self, task_id: str, user_id: str, task_data: Dict[str, Any]):
        """Register a new automation task"""
        try:
            task_info = {
                "task_id": task_id,
                "user_id": user_id,
                "status": TaskStatus.PENDING.value,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                **task_data
            }
            
            # Store in memory
            self.task_registry[task_id] = task_info
            
            # Store in Redis
            await self.redis.hset(f"task:{task_id}", mapping=task_info)
            await self.redis.expire(f"task:{task_id}", 3600)  # 1 hour expiration
            
            self.log_info(f"Registered task: {task_id}")
            
        except Exception as e:
            self.log_error(f"Failed to register task {task_id}: {e}")
            raise SessionException(f"Task registration failed: {e}")
    
    async def update_task_status(self, task_id: str, status: TaskStatus, metadata: Dict[str, Any] = None):
        """Update task status"""
        try:
            if task_id in self.task_registry:
                self.task_registry[task_id]["status"] = status.value
                self.task_registry[task_id]["updated_at"] = datetime.now().isoformat()
                
                if metadata:
                    self.task_registry[task_id].update(metadata)
                
                # Update Redis
                await self.redis.hset(f"task:{task_id}", mapping=self.task_registry[task_id])
            
        except Exception as e:
            self.log_error(f"Failed to update task status {task_id}: {e}")
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get current task status"""
        try:
            # Check memory first
            if task_id in self.task_registry:
                return self.task_registry[task_id]
            
            # Check Redis
            task_data = await self.redis.hgetall(f"task:{task_id}")
            if task_data:
                self.task_registry[task_id] = task_data
                return task_data
            
            return None
            
        except Exception as e:
            self.log_error(f"Failed to get task status {task_id}: {e}")
            return None
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a running task"""
        try:
            task_info = await self.get_task_status(task_id)
            if not task_info:
                return False
            
            if task_info["status"] in [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value, TaskStatus.CANCELLED.value]:
                return False
            
            await self.update_task_status(task_id, TaskStatus.CANCELLED)
            self.log_info(f"Cancelled task: {task_id}")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to cancel task {task_id}: {e}")
            return False
    
    async def get_monthly_usage(self, user_id: str) -> int:
        """Get monthly browser automation usage for a user"""
        try:
            # Get current month key
            current_month = datetime.now().strftime("%Y-%m")
            usage_key = f"usage:{user_id}:{current_month}"
            
            usage = await self.redis.get(usage_key)
            return int(usage) if usage else 0
            
        except Exception as e:
            self.log_error(f"Failed to get monthly usage for {user_id}: {e}")
            return 0
    
    async def increment_usage(self, user_id: str):
        """Increment monthly usage for a user"""
        try:
            current_month = datetime.now().strftime("%Y-%m")
            usage_key = f"usage:{user_id}:{current_month}"
            
            await self.redis.incr(usage_key)
            # Set expiration to end of next month
            next_month = datetime.now().replace(day=1) + timedelta(days=32)
            next_month = next_month.replace(day=1)
            await self.redis.expireat(usage_key, int(next_month.timestamp()))
            
        except Exception as e:
            self.log_error(f"Failed to increment usage for {user_id}: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for session manager"""
        try:
            # Test Redis connection
            await self.redis.ping()
            
            return {
                "redis_connected": True,
                "active_sessions": len(self.active_sessions),
                "active_tasks": len(self.task_registry),
                "status": "healthy"
            }
            
        except Exception as e:
            self.log_error(f"Health check failed: {e}")
            return {
                "redis_connected": False,
                "error": str(e),
                "status": "unhealthy"
            }
    
    async def _store_session_in_redis(self, session: BrowserSession):
        """Store session data in Redis"""
        session_data = session.dict()
        # Convert datetime objects to ISO strings
        for key, value in session_data.items():
            if isinstance(value, datetime):
                session_data[key] = value.isoformat()
        
        await self.redis.hset(f"session:{session.session_id}", mapping=session_data)
        await self.redis.expire(f"session:{session.session_id}", settings.SESSION_TIMEOUT)
    
    async def _cleanup_expired_sessions(self):
        """Background task to cleanup expired sessions"""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                
                current_time = datetime.now()
                expired_sessions = []
                
                for session_id, session in self.active_sessions.items():
                    if (current_time - session.last_activity).seconds > settings.SESSION_TIMEOUT:
                        expired_sessions.append(session_id)
                
                for session_id in expired_sessions:
                    await self.close_session(session_id)
                    self.log_info(f"Cleaned up expired session: {session_id}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error(f"Error in session cleanup: {e}")
